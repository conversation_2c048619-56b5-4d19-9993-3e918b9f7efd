<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-webpage-customer</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-webpage-customer-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-common-beans</artifactId>
            <version>${fs-qixin-common-beans.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-cep-spring-plugin</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-rest-client-core</artifactId>
            <version>6.6.5-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-oauth-base-api</artifactId>
            <version>2.1.0-SNAPSHOT</version>
        </dependency>
        <!--CRM通知接口 -> 主要用于获取未读 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-notify-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-restful-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-rocketmq-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-enterprise-relation-api</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-paas-autn-clent</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-metadata-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-enterprise-linkapp-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-enterprise-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-paas-app-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-qixin-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>retrofit-spring2</artifactId>
            <version>2.0.3-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-parallel</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-enterprise-common</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-crm-rest-api</artifactId>
            <version>2.0.17-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
package com.facishare.webpage.customer.api.service;


import com.facishare.webpage.customer.api.model.arg.*;

/**
 * Created by <PERSON><PERSON> on 19/12/13.
 */
public interface MenusRegisterService {

    /**
     * 创建menus属于租户下属于某一个collectionId的菜单, 支持app端页面和web端菜单
     */
    CreateMenus.Result createMenus(CreateMenus.Arg arg);

    /**
     * 删除menus
     */
    DeleteMenus.Result deleteMenus(DeleteMenus.Arg arg);

    UpdateMenus.Result updateMenus(UpdateMenus.Arg arg);


    /**
     *根据collectionId查询
     */

    QueryMenusByCollectionIds.Result queryMenusByCollectionIds(QueryMenusByCollectionIds.Arg arg);

    QueryMenus.Result queryMenus(QueryMenus.Arg arg);
}

package com.facishare.webpage.customer.api.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SiteInfoPO {
    private String id;
    private String apiName;
    private String name;
    private String description;
    private String siteId;
    private Boolean needLogin;
    private String appId;
    private Integer status;
    private Integer creatorId;
    private Long createTime;
    private Integer updaterId;
    private Long updateTime;
    private Integer publishStatus;
    private Long publishTime;
    private Integer publisherId;
    private Long publishVersion;
    private Integer appPublishStatus;
    private Long appPublishTime;
    private Integer appPublisherId;
    private Long appPublishVersion;
} 
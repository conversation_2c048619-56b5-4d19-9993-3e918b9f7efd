<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-webpage-customer</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-webpage-customer-core</artifactId>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!--加载配置-->
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>spring-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-cep-spring-plugin</artifactId>
            <version>${fs-cep-spring-plugin.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>


        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-webpage-customer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-url</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-adapter-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-rest-client-common-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-rocketmq-support</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-i18n</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-dubbo-rest-plugin</artifactId>
            <version>${dubbo2rest.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-license-api</artifactId>
        </dependency>

    </dependencies>

</project>

package com.facishare.webpage.customer.core.codec;

import com.alibaba.fastjson.JSONObject;
import com.facishare.rest.core.codec.AbstractRestCodeC;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.core.util.JsonUtil;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class BiReportCodec extends AbstractRestCodeC {
    @Override
    public <T> byte[] encodeArg(T obj) {
        if (Objects.isNull(obj)) {
            return null;
        } else {
            return obj instanceof String ? ((String) obj).getBytes(StandardCharsets.UTF_8) : JsonUtil.toJsonWithNull(obj).getBytes(StandardCharsets.UTF_8);
        }
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        String bodyString = new String(bytes, StandardCharsets.UTF_8);
        if (statusCode != 200) {
            throw new RestProxyRuntimeException(statusCode, bodyString);
        }
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(bodyString);
        } catch (Exception e) {
            throw new RestProxyRuntimeException(statusCode, bodyString);
        }
        Integer code = jsonObject.getInteger("code");
        if (200 != code) {
            throw new RestProxyRuntimeException(statusCode, bodyString);
        }
        try {
            return jsonObject.getObject("data", clazz);
        } catch (Exception e) {
            throw new RestProxyRuntimeException(statusCode, bodyString);
        }




    }
}

package com.facishare.webpage.customer.core.config;

import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import com.facishare.webpage.customer.core.model.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by she<PERSON> on 19/12/11.
 */
public class CustomerCoreConfig {

    @Resource
    private MenusConfig menusConfig;

    @Resource
    private WidgetsConfig widgetsConfig;

    @Resource
    private MenusCollectionConfig menusCollectionConfig;

    @Resource
    private WidgetCollectionConfig widgetCollectionConfig;


    public List<ComponentDto> convert2Dto(List<Component> components, Map<String, List<Menu>> menus) {
        List<ComponentDto> componentDtos = Lists.newArrayList();
        components.stream().forEach(component -> {
            List<Menu> registerMenus = Lists.newArrayList();
            if (menus != null) {
                registerMenus = menus.get(component.getCollectionId());
            }
            List<ComponentDto> dtos = convert2Dto(component, registerMenus);
            componentDtos.addAll(dtos);
        });
        return componentDtos;
    }

    public List<ComponentDto> convert2Dto(List<Component> components) {
        List<ComponentDto> componentDtos = Lists.newArrayList();
        components.stream().forEach(component -> {
            List<ComponentDto> dtos = convert2Dto(component, null);
            componentDtos.addAll(dtos);
        });
        return componentDtos;
    }

    public List<ComponentDto> convert2Dto(List<Component> components, UserInfo userInfo, Map<String, List<Menu>> registeredMenuMap) {
        if (CollectionUtils.isEmpty(components)) {
            return Lists.newArrayList();
        }
        List<ComponentDto> componentDtos = Lists.newArrayList();
        Map<String, List<Menu>> finalRegisteredMenuMap = Objects.isNull(registeredMenuMap) ? Maps.newHashMap() : registeredMenuMap;
        components.forEach(component -> {
            List<ComponentDto> dtos = convert2Dto(component,
                    finalRegisteredMenuMap.getOrDefault(component.getCollectionId(), Lists.newArrayList()));
            if (CollectionUtils.isEmpty(component.getGrayEiList())) {
                componentDtos.addAll(dtos);
            } else {
                if (component.getGrayEiList().contains(userInfo.getEnterpriseId())) {
                    componentDtos.addAll(dtos);
                }
            }
        });
        return componentDtos;
    }

    private List<ComponentDto> convert2Dto(Component component, List<Menu> registerMenus) {
        List<ComponentDto> componentDtos = Lists.newArrayList();
        ComponentDto componentDto = new ComponentDto();
        componentDtos.add(componentDto);

        componentDto.setId(component.getId());
        componentDto.setParentId(component.getParentId());
        componentDto.setComponentType(component.getComponentType());
        componentDto.setTitle(component.getTitle());
        componentDto.setTitleI18nKey(component.getTitleI18nKey());
        componentDto.setWidgetSourceType(component.getWidgetSourceType());

        switch (component.getComponentType()) {
            case ComponentTypConst.GROUP_TYPE:
                return componentDtos;
            case ComponentTypConst.MENU_COLLECTION_TYPE:
                MenuCollection menuCollection = menusCollectionConfig.getMenuCollection(component.getCollectionId());
                if (menuCollection == null) {
                    return componentDtos;
                }
                if (menuCollection.getMenus() != null) {
                    List<Menu> menus = menusConfig.getMenusByIds(menuCollection.getMenus());
                    if (registerMenus != null) {
                        menus.addAll(registerMenus);
                    }
                    componentDto.setMenus(menus);
                }
                componentDto.setMenuSourceType(menuCollection.getMenuSourceType());
                componentDto.setType(menuCollection.getType());
                componentDto.setTenantPrivilege(menuCollection.getTenantPrivilege());
                if (component.getTenantPrivilege() != null) {
                    componentDto.setTenantPrivilege(component.getTenantPrivilege());
                }
                return componentDtos;
            case ComponentTypConst.WIDGET_COLLECTION_TYPE:
                WidgetCollection widgetCollection = widgetCollectionConfig.getWidgetCollection(component.getCollectionId());
                if (widgetCollection == null) {
                    return componentDtos;
                }
                if (!CollectionUtils.isEmpty(widgetCollection.getWidgets())) {
                    List<Widget> widgets = widgetsConfig.getWidgetsByIds(widgetCollection.getWidgets());
                    TenantPrivilege tenantPrivilege = widgetCollection.getTenantPrivilege();
                    if (component.getTenantPrivilege() != null) {
                        tenantPrivilege = component.getTenantPrivilege();
                    }
                    TenantPrivilege finalTenantPrivilege = tenantPrivilege;
                    List<ComponentDto> wComponent = widgets.stream().map(widget -> convert2Component(component.getId(), widget, finalTenantPrivilege, ComponentTypConst.WIDGET_TYPE)).collect(Collectors.toList());
                    componentDtos.addAll(wComponent);
                }
                componentDto.setWidgetSourceType(widgetCollection.getWidgetSourceType());
                return componentDtos;
            case ComponentTypConst.WIDGET_TYPE:
                Widget widget = widgetsConfig.getWidget(component.getId());
                componentDto.setWidget(widget);
                componentDto.setTenantPrivilege(component.getTenantPrivilege());
                return componentDtos;
            default:
                return componentDtos;
        }
    }

    public ComponentDto convert2Component(String componentId, Widget widget, TenantPrivilege tenantPrivilege, int componentType) {
        ComponentDto componentDto = new ComponentDto();
        componentDto.setWidget(widget);
        componentDto.setId(widget.getId());
        if (tenantPrivilege != null && !Strings.isNullOrEmpty(tenantPrivilege.getAppId()) && widget != null) {
            componentDto.setId(tenantPrivilege.getAppId() + "_" + widget.getId());
        }
        componentDto.setTitle(widget.getName());
        componentDto.setComponentType(componentType);
        componentDto.setParentId(componentId);
        componentDto.setTenantPrivilege(tenantPrivilege);
        return componentDto;
    }


}

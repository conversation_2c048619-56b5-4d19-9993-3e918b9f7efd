package com.facishare.webpage.customer.core.resource;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.webpage.customer.api.model.GetDashBoardList;
import com.facishare.webpage.customer.api.model.GetGlobalFilter;
import com.facishare.webpage.customer.core.model.BIItemKey;

import java.util.Map;

/**
 * <AUTHOR> Yu
 * @date 2022/3/8 11:15 AM
 */
@RestResource(
        value = "BiReportResource",
        desc = "fs-bi-crm-report-web",
        contentType = "application/json")
public interface BiReportResource {
    /**
     * 根据ID获取驾驶舱信息  // ignoreI18n
     *
     * @param headers
     * @param arg
     * @return
     */
    @POST(
            value = "/api/v1/dashboard/getDashBoardList",
            desc = "根据ID获取驾驶舱信息" // ignoreI18n
    )
    GetDashBoardList.Result getDashBoardList(@HeaderMap Map<String, String> headers, @Body GetDashBoardList.Arg arg);

    @POST(value = "/api/v1/component/parseGlobalFilters", desc = "根据Filter获取拍平后的人员信息（BI筛选器用）")
        // ignoreI18n
    GetGlobalFilter.Result getGlobalFilter(@HeaderMap Map<String, String> headers, @Body GetGlobalFilter.Arg arg);

    @POST(value = "/api/v1/i18n/getI18nKeys", desc = "根据企业及cardId批量查询bi组件多语信息", // ignoreI18n
            codec = "com.facishare.webpage.customer.core.codec.BiReportCodec")
    BIItemKey.Result getI18nKeys(@HeaderMap Map<String, String> headers, @Body BIItemKey.Arg arg);


}

package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.ThemeStyleEntity;

import java.util.List;

public interface ThemeStyleEntityDao {

    List<ThemeStyleEntity> findBySiteApiNameIncludeDisable(int tenantId, String siteApiName, String clientType);

    List<ThemeStyleEntity> findByApiNamesIncludeDisable(int tenantId, List<String> apiNames);

    List<ThemeStyleEntity> findByApiNames(int tenantId, List<String> apiNames, String clientType);

    void batchSave(User user, List<ThemeStyleEntity> entityList);

    void batchUpdate(User user, List<ThemeStyleEntity> entityList);

    void updateStatus(User user, List<String> ids, int status);
}

package com.facishare.webpage.customer.controller.model.arg.portal;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.dao.entity.SiteConfigEntity;
import com.facishare.webpage.customer.dao.entity.SiteEntity;

import lombok.Data;

@Data
public class SiteConfigDTO {
    private JSONObject configData;
    private String cssData;

    public SiteConfigEntity toEntity(SiteEntity siteEntity, String clientType) {
        SiteConfigEntity siteConfigEntity = new SiteConfigEntity();
        siteConfigEntity.setSiteApiName(siteEntity.getApiName());
        siteConfigEntity.setClientType(clientType);
        siteConfigEntity.setConfigData(configData);
        siteConfigEntity.setCssData(cssData);
        return siteConfigEntity;
    }
}


package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.dao.entity.SiteConfigEntity;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import com.facishare.webpage.customer.api.model.User;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.PostConstruct;
import java.util.Objects;

import lombok.Setter;

@Setter
public class SiteConfigEntityDaoImpl implements SiteConfigEntityDao {

    private Datastore datastore;

    @PostConstruct
    public void init() {
        datastore.ensureIndexes(SiteConfigEntity.class, true);
    }

    @Override
    public SiteConfigEntity findBySiteApiName(int tenantId, String siteApiName, String clientType) {
        return datastore.createQuery(SiteConfigEntity.class)
                .field("tenantId").equal(tenantId)
                .field("siteApiName").equal(siteApiName)
                .field("clientType").equal(clientType)
                .get();
    }

    @Override
    public void save(User user, SiteConfigEntity entity) {
        SiteConfigEntity dbEntity = findBySiteApiName(user.getTenantId(), entity.getSiteApiName(), entity.getClientType());
        if (Objects.nonNull(dbEntity)) {
            entity.setId(dbEntity.getId());
            update(user, entity);
        } else {
            if (StringUtils.isBlank(entity.getId())) {
                entity.setId(ObjectId.get().toString());
            }
            entity.setTenantId(user.getTenantId());
            entity.setCreatorId(user.getUserId());
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdaterId(user.getUserId());
            entity.setUpdateTime(entity.getCreateTime());
            datastore.save(entity);
        }
    }

    @Override
    public void update(User user, SiteConfigEntity entity) {
        Query<SiteConfigEntity> query = datastore.createQuery(SiteConfigEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("_id").equal(entity.getId());
        UpdateOperations<SiteConfigEntity> updateOperations = datastore.createUpdateOperations(SiteConfigEntity.class);
        if (Objects.nonNull(entity.getConfigData())) {
            updateOperations.set("configData", entity.getConfigData());
        }
        if (Objects.nonNull(entity.getCssData())) {
            updateOperations.set("cssData", entity.getCssData());
        }
        updateOperations.set("updateTime", System.currentTimeMillis());
        updateOperations.set("updaterId", user.getUserId());
        datastore.findAndModify(query, updateOperations, false);
    }
}

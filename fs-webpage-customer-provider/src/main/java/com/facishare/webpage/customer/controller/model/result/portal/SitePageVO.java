package com.facishare.webpage.customer.controller.model.result.portal;

import java.util.List;
import java.util.Objects;

import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.model.LayoutStructure;

import lombok.Data;

/**
 * Created by zhouwr on 2024/11/4.
 */
@Data
public class SitePageVO {
    private String id;
    private String apiName;
    private String name;
    private String description;
    private LayoutStructure layoutStructure;
    private Boolean isHomePage = false;
    private String themeLayoutApiName;
    private Integer type;
    private String objectApiName;
    private Boolean needLogin;
    private String siteApiName;
    private String appId;
    private Integer creatorId;
    private Long createTime;
    private Integer updaterId;
    private Long updateTime;
    private List<String> scopes;
    private String themeStyleApiName;

    public static SitePageVO of(HomePageLayoutEntity entity) {
        SitePageVO vo = new SitePageVO();
        vo.setId(entity.getLayoutId());
        vo.setApiName(entity.getApiName());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setLayoutStructure(Objects.isNull(entity.getCustomerLayout()) ? null : new LayoutStructure(entity.getCustomerLayout()));
        vo.setIsHomePage(entity.getIsHomePage());
        vo.setThemeLayoutApiName(entity.getThemeLayoutApiName());
        vo.setType(entity.getAppType());
        vo.setObjectApiName(entity.getObjectApiName());
        vo.setNeedLogin(entity.getNeedLogin());
        vo.setSiteApiName(entity.getSiteApiName());
        vo.setAppId(entity.getAppId());
        vo.setCreatorId(entity.getCreatorId());
        vo.setScopes(entity.getScopes());
        vo.setThemeStyleApiName(entity.getThemeStyleApiName());
        if (Objects.nonNull(entity.getCreateTime())) {
            vo.setCreateTime(entity.getCreateTime().getTime());
        }
        vo.setUpdaterId(entity.getUpdaterId());
        if (Objects.nonNull(entity.getUpdateTime())) {
            vo.setUpdateTime(entity.getUpdateTime().getTime());
        }
        return vo;
    }
}

package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.dao.entity.SiteConfigEntity;
import com.facishare.webpage.customer.api.model.User;

public interface SiteConfigEntityDao {
    SiteConfigEntity findBySiteApiName(int tenantId, String siteApiName, String clientType);
    void save(User user, SiteConfigEntity siteConfigEntity);
    void update(User user, SiteConfigEntity siteConfigEntity);
}

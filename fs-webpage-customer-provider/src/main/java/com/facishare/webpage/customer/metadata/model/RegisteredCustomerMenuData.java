package com.facishare.webpage.customer.metadata.model;

import com.alibaba.fastjson.JSON;
import com.facishare.webpage.customer.api.model.core.Url;
import com.facishare.webpage.customer.dao.entity.MenuEntity;
import com.facishare.webpage.customer.util.CustomerMenuUtil;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/7/21
 * @Description :
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class RegisteredCustomerMenuData extends CustomerMenuData {
    private MenuEntity menuEntity;
    private Url url;

    public static RegisteredCustomerMenuData of(@NonNull MenuEntity entity, String appId) {
        RegisteredCustomerMenuData registeredCustomerMenuData = of(entity);
        registeredCustomerMenuData.setAppId(appId);
        return registeredCustomerMenuData;
    }


    public static RegisteredCustomerMenuData of(@NonNull MenuEntity entity){
        RegisteredCustomerMenuData registeredCustomerMenuData = new RegisteredCustomerMenuData();
        registeredCustomerMenuData.setCustomerMenu(CustomerMenuUtil.covertCustomerMenuByMenuEntity(null, entity));  // 企业内使用, 暂时不用考虑appName
        registeredCustomerMenuData.setMenuEntity(entity);
        try {
            String urlStr = entity.getUrl();
            Url url = Strings.isNullOrEmpty(urlStr) ? null : JSON.parseObject(urlStr, Url.class);
            registeredCustomerMenuData.setUrl(url);
        } catch (Exception e){
            log.warn("buid url error, ei: {}, menuApi: {}, collectionId: {}", entity.getTenantId(), entity.getApiName(), entity.getCollectionId());
        }
        return registeredCustomerMenuData;
    }
}

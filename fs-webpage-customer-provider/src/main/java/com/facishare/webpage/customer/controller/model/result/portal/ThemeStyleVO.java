package com.facishare.webpage.customer.controller.model.result.portal;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.dao.entity.ThemeStyleEntity;
import lombok.Data;

@Data
public class ThemeStyleVO {
    private String apiName;
    private String name;
    private JSONObject styleConfig;
    private Integer creatorId;
    private Long createTime;
    private Integer updaterId;
    private Long updateTime;

    public static ThemeStyleVO of(ThemeStyleEntity entity) {
        ThemeStyleVO vo = new ThemeStyleVO();
        vo.setApiName(entity.getApiName());
        vo.setName(entity.getName());
        vo.setStyleConfig(entity.getStyleConfig());
        vo.setCreatorId(entity.getCreatorId());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdaterId(entity.getUpdaterId());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }
}

package com.facishare.webpage.customer.dao.entity;

import com.facishare.webpage.customer.api.constant.ClientType;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

@Data
@Entity(value = "SiteConfigEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {
                @Field("tenantId"),
                @Field("siteApiName"),
                @Field("clientType")}, options = @IndexOptions(background = true, unique = true))
})
public class SiteConfigEntity {
    @Property("configData")
    private JSONObject configData;
    @Property("cssData")
    private String cssData;
    @Property("siteApiName")
    private String siteApiName;
    @Property("clientType")
    private String clientType = ClientType.web.getValue();  
    @Id
    private String id;
    @Property("tenantId")
    private int tenantId;
    @Property("creatorId")
    private Integer creatorId;
    @Property("createTime")
    private Long createTime;
    @Property("updaterId")
    private Integer updaterId;
    @Property("updateTime")
    private Long updateTime;
}

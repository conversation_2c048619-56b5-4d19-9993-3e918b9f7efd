package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.constant.PaaSStatus;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.ThemeStyleEntity;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

@Setter
public class ThemeStyleEntityDaoImpl implements ThemeStyleEntityDao {

    private Datastore datastore;

    @PostConstruct
    public void init() {
        datastore.ensureIndexes(ThemeStyleEntity.class, true);
    }

    private Query<ThemeStyleEntity> buildQuery(int tenantId, String clientType) {
        Query<ThemeStyleEntity> query = datastore.createQuery(ThemeStyleEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("clientType").equal(clientType);
        return query;
    }

    @Override
    public List<ThemeStyleEntity> findBySiteApiNameIncludeDisable(int tenantId, String siteApiName, String clientType) {
        Query<ThemeStyleEntity> query = buildQuery(tenantId, clientType);
        query.field("siteApiName").equal(siteApiName);
        query.field("status").in(Lists.newArrayList(PaaSStatus.enable, PaaSStatus.disable));
        query.order("createTime");
        return query.asList();
    }

    @Override
    public List<ThemeStyleEntity> findByApiNamesIncludeDisable(int tenantId, List<String> apiNames) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return Lists.newArrayList();
        }
        Query<ThemeStyleEntity> query = datastore.createQuery(ThemeStyleEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNames);
        query.field("status").in(Lists.newArrayList(PaaSStatus.enable, PaaSStatus.disable));
        query.order("createTime");
        return query.asList();
    }

    @Override
    public List<ThemeStyleEntity> findByApiNames(int tenantId, List<String> apiNames, String clientType) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return Lists.newArrayList();
        }
        Query<ThemeStyleEntity> query = buildQuery(tenantId, clientType);
        query.field("apiName").in(apiNames);
        query.field("status").equal(PaaSStatus.enable);
        query.order("createTime");
        return query.asList();
    }

    @Override
    public void batchSave(User user, List<ThemeStyleEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        entityList.forEach(entity -> {
            if (StringUtils.isBlank(entity.getId())) {
                entity.setId(ObjectId.get().toHexString());
            }
            entity.setTenantId(user.getTenantId());
            entity.setCreatorId(user.getUserId());
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdaterId(user.getUserId());
            entity.setUpdateTime(entity.getCreateTime());
            if (Objects.isNull(entity.getStatus())) {
                entity.setStatus(PaaSStatus.enable);
            }
            if (Strings.isNullOrEmpty(entity.getSourceType())) {
                entity.setSourceType(SourceType.CUSTOMER);
            }
        });
        datastore.save(entityList);
    }

    @Override
    public void batchUpdate(User user, List<ThemeStyleEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        long now = System.currentTimeMillis();
        entityList.forEach(entity -> {
            Query<ThemeStyleEntity> query = datastore.createQuery(ThemeStyleEntity.class);
            query.field("tenantId").equal(user.getTenantId());
            query.field("_id").equal(entity.getId());
            UpdateOperations<ThemeStyleEntity> updateOperations = datastore.createUpdateOperations(ThemeStyleEntity.class);
            if (Objects.nonNull(entity.getName())) {
                updateOperations.set("name", entity.getName());
            }
            if (Objects.nonNull(entity.getStyleConfig())) {
                updateOperations.set("styleConfig", entity.getStyleConfig());
            }
            updateOperations.set("updateTime", now);
            updateOperations.set("updaterId", user.getUserId());
            datastore.findAndModify(query, updateOperations, false);
        });
    }

    @Override
    public void updateStatus(User user, List<String> ids, int status) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        Query<ThemeStyleEntity> query = datastore.createQuery(ThemeStyleEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("_id").in(ids);
        query.field("status").notEqual(status);
        UpdateOperations<ThemeStyleEntity> updateOperations = datastore.createUpdateOperations(ThemeStyleEntity.class);
        updateOperations.set("status", status);
        updateOperations.set("updateTime", System.currentTimeMillis());
        updateOperations.set("updaterId", user.getUserId());
        if (status == PaaSStatus.delete) {
            updateOperations.set("deleteId", ObjectId.get().toString());
        }
        datastore.update(query, updateOperations);
    }
} 
package com.facishare.webpage.customer.dao.entity;

import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.constant.ClientType;

import lombok.Data;

@Data
@Entity(value = "ThemeStyleInfoEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {
                @Field("tenantId"),
                @Field("apiName"),
                @Field("deleteId")}, options = @IndexOptions(background = true, unique = true)),
        @Index(fields = {
                @Field("tenantId"),
                @Field("siteApiName")}, options = @IndexOptions(background = true)),
        @Index(fields = {
                @Field("tenantId"),
                @Field("appId")}, options = @IndexOptions(background = true))
})
public class ThemeStyleEntity {

    @Property("apiName")
    private String apiName;
    @Property("name")
    private String name;
    @Property("styleConfig")
    private JSONObject styleConfig;
    @Property("appId")
    private String appId;
    @Property("siteApiName")
    private String siteApiName;
    @Property("clientType")
    private String clientType = ClientType.web.getValue();
    @Property("status")
    private Integer status;
    @Property("sourceType")
    private String sourceType;
    @Property("tenantId")
    private int tenantId;
    @Id
    private String id;
    @Property("creatorId")
    private Integer creatorId;
    @Property("createTime")
    private Long createTime;
    @Property("updaterId")
    private Integer updaterId;
    @Property("updateTime")
    private Long updateTime;
    @Property("deleteId")
    private String deleteId;
}

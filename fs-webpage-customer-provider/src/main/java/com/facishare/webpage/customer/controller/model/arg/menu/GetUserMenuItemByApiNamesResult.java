package com.facishare.webpage.customer.controller.model.arg.menu;

import com.facishare.webpage.customer.api.model.UserMenuItem;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/4/15
 */
@Data
@AllArgsConstructor(staticName = "of")
public class GetUserMenuItemByApiNamesResult extends BaseResult {
    private List<UserMenuItem> menuItems;
}

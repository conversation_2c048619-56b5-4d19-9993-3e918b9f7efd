package com.facishare.webpage.customer.metadata;

import com.facishare.qixin.common.monitor.GlobalStopWatch;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.webpage.customer.dao.entity.MenuEntity;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataArg;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataResult;
import com.facishare.webpage.customer.metadata.model.RegisteredCustomerMenuData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/7/21
 * @Description :
 */
@Service("registeredMenuMetaDataService")
public class RegisteredMenuMetaDataService implements MetaDataService {

    @Resource
    private InternalMenusRegisterService internalMenusRegisterService;

    @Override
    public QueryMetaDataResult queryMetaData(QueryMetaDataArg arg) {
        int tenantId = arg.getTenantId();
        SlowLog slowLog = GlobalStopWatch.create("queryRegisteredMenuMetaData", 100L);
        List<MenuEntity> registerMenuDataList = internalMenusRegisterService.queryMenusByTenantId(tenantId);
        slowLog.lap("RegisteredMenuMetaData end");
        slowLog.stop("queryRegisteredMenuMetaData");
        return QueryMetaDataResult.builder().metaMenuDataList(registerMenuDataList.stream()
                .filter(Objects::nonNull)
                .map(entity -> RegisteredCustomerMenuData.of(entity, arg.getOldAppId()))
                .collect(Collectors.toList())).build();
    }
}

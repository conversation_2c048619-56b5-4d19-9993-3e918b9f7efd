package com.facishare.webpage.customer.controller.model.arg.portal;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.dao.entity.SiteEntity;
import com.facishare.webpage.customer.dao.entity.ThemeStyleEntity;

import lombok.Data;

@Data
public class ThemeStyleDTO {
    private String apiName;
    private String name;
    private JSONObject styleConfig;

    public ThemeStyleEntity toEntity(SiteEntity siteEntity, String clientType) {
        ThemeStyleEntity entity = new ThemeStyleEntity();
        entity.setAppId(siteEntity.getAppId());
        entity.setSiteApiName(siteEntity.getApiName());
        entity.setClientType(clientType);
        entity.setApiName(apiName);
        entity.setName(name);
        entity.setStyleConfig(styleConfig);
        return entity;
    }
}

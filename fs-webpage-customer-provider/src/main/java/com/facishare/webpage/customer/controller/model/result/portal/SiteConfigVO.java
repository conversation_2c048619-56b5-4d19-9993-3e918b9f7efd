package com.facishare.webpage.customer.controller.model.result.portal;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.dao.entity.SiteConfigEntity;
import lombok.Data;

@Data
public class SiteConfigVO {
    private JSONObject configData;
    private String cssData;

    public static SiteConfigVO of(SiteConfigEntity entity) {
        SiteConfigVO vo = new SiteConfigVO();
        vo.setConfigData(entity.getConfigData());
        vo.setCssData(entity.getCssData());
        return vo;
    }
}
